-- Base de données Facturation - Script d'initialisation

-- Table: Secteur
CREATE TABLE IF NOT EXISTS Secteur (
    idS SERIAL PRIMARY KEY,
    nom VARCHAR(100)
);

-- Table: Client
CREATE TABLE IF NOT EXISTS Client (
    idClient SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    ville VARCHAR(100),
    tel VARCHAR(20),
    email VARCHAR(100),
    idS INT REFERENCES Secteur(idS)
);

-- Table: Utilisateur
CREATE TABLE IF NOT EXISTS Utilisateur (
    idTech SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    motDepass VARCHAR(100),
    role VARCHAR(10) CHECK (role IN ('Admin', 'Tech'))
);

-- Table: Contract
CREATE TABLE IF NOT EXISTS Contract (
    idContract SERIAL PRIMARY KEY,
    codeQr VARCHAR(100),
    dateContract TIMESTAMP,
    idClient INT REFERENCES Client(idClient),
    marqueCompteur VARCHAR(100),
    numSerieCompteur VARCHAR(100),
    posX VARCHAR(50),
    posY VARCHAR(50)
);

-- Table: Tranch
CREATE TABLE IF NOT EXISTS Tranch (
    idTranch SERIAL PRIMARY KEY,
    prix FLOAT,
    valeurMin FLOAT,
    valeurMax FLOAT
);

-- Table: Consommation
CREATE TABLE IF NOT EXISTS Consommation (
    idCons SERIAL PRIMARY KEY,
    consommationPre INT,
    consommationActuelle INT,
    idCont INT REFERENCES Contract(idContract),
    idTech INT REFERENCES Utilisateur(idTech),
    idTranch INT REFERENCES Tranch(idTranch),
    jours INT,
    periode VARCHAR(50),
    status VARCHAR(50)
);

-- Table: Facture
CREATE TABLE IF NOT EXISTS Facture (
    idFact SERIAL PRIMARY KEY,
    date TIMESTAMP,
    idConst INT REFERENCES Consommation(idCons),
    montant FLOAT,
    periode VARCHAR(50),
    reference INT,
    status VARCHAR(10) CHECK (status IN ('payée', 'nonpayée'))
);

-- Insertion des données de test
INSERT INTO Secteur (nom) VALUES 
('Secteur Nord'),
('Secteur Sud'),
('Secteur Est'),
('Secteur Ouest')
ON CONFLICT DO NOTHING;

-- Insertion des utilisateurs de test
INSERT INTO Utilisateur (nom, prenom, adresse, tel, email, motDepass, role) VALUES 
('Admin', 'Système', '123 Rue Principale', '0123456789', '<EMAIL>', 'admin123', 'Admin'),
('Dupont', 'Jean', '456 Avenue des Fleurs', '0987654321', '<EMAIL>', 'tech123', 'Tech'),
('Martin', 'Marie', '789 Boulevard Central', '0555666777', '<EMAIL>', 'tech456', 'Tech')
ON CONFLICT (email) DO NOTHING;

-- Insertion de clients de test
INSERT INTO Client (nom, prenom, adresse, ville, tel, email, idS) VALUES 
('Benali', 'Ahmed', '12 Rue de la Paix', 'Casablanca', '0612345678', '<EMAIL>', 1),
('Alami', 'Fatima', '34 Avenue Mohammed V', 'Rabat', '0698765432', '<EMAIL>', 2),
('Tazi', 'Omar', '56 Boulevard Hassan II', 'Fès', '0677889900', '<EMAIL>', 3)
ON CONFLICT DO NOTHING;
