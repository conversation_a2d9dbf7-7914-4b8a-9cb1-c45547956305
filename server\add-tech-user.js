require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

async function addTechUser() {
  try {
    console.log('🔍 Vérification si l\'utilisateur <EMAIL> existe...');
    
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (existingUser.rows.length > 0) {
      console.log('✅ L\'utilisateur <EMAIL> existe déjà');
      console.log('📝 Mise à jour du mot de passe...');
      
      // Mettre à jour le mot de passe
      await pool.query(
        'UPDATE Utilisateur SET motdepass = $1 WHERE email = $2',
        ['Tech123', '<EMAIL>']
      );
      
      console.log('✅ Mot de passe mis à <NAME_EMAIL>');
    } else {
      console.log('➕ Ajout du <NAME_EMAIL>...');
      
      // Ajouter le nouvel utilisateur
      await pool.query(`
        INSERT INTO Utilisateur (nom, prenom, adresse, tel, email, motdepass, role)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        'Technicien',
        'Principal', 
        '456 Rue Technique',
        '0987654321',
        '<EMAIL>',
        'Tech123',
        'Tech'
      ]);
      
      console.log('✅ Utilisateur <EMAIL> ajouté avec succès');
    }
    
    // Afficher tous les utilisateurs
    console.log('\n📋 Liste des utilisateurs dans la base :');
    const allUsers = await pool.query('SELECT email, role, motdepass FROM Utilisateur ORDER BY email');
    allUsers.rows.forEach(user => {
      console.log(`- ${user.email} (${user.role}) - mot de passe: ${user.motdepass}`);
    });
    
    console.log('\n🔑 Comptes de connexion disponibles :');
    console.log('- <EMAIL> / admin123 (Admin)');
    console.log('- <EMAIL> / Tech123 (Tech)');
    console.log('- <EMAIL> / tech123 (Tech)');
    console.log('- <EMAIL> / tech456 (Tech)');
    
  } catch (error) {
    console.error('❌ Erreur:', error);
  } finally {
    await pool.end();
  }
}

addTechUser();
