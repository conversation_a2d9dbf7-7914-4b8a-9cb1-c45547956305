require('dotenv').config();
const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const { router: factureRouter, generateFactureAutomatique } = require('./facture');
const clientsRouter = require('./clients');
const app = express();
app.use(express.json());
app.use(cors());

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Utilisation des routes de factures
app.use('/', factureRouter);
// Utilisation des routes de clients
app.use('/', clientsRouter);

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration', // Nom correct de la base
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de test
app.get('/', (req, res) => {
  res.send('Serveur Facutration fonctionnel');
});

// Route de connexion adaptée à votre table Utilisateur
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body;

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // Recherche de l'utilisateur dans la table Utilisateur
    const result = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1',
      [email]
    );

    if (result.rows.length === 0) {
      console.log('Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('Utilisateur trouvé:', { email: user.email, role: user.role });

    // Comparaison directe du mot de passe (en clair dans votre table)
    if (motDepass !== user.motdepass) {
      console.log('Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Connexion réussie
    console.log('Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (err) {
    console.error('Erreur lors de la connexion:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: err.message
    });
  }
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Récupération de tous les clients depuis la table Client');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        COALESCE(s.nom, 'Non défini') as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);
    console.log(`✅ ${result.rows.length} clients récupérés depuis la table Client`);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// 🎯 ROUTE CRITIQUE : Récupérer les contrats d'un client spécifique
app.get('/api/clients/:id/contracts', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`\n🎯 ROUTE CRITIQUE: GET /api/clients/${id}/contracts`);
    console.log(`🔍 Recherche des contrats pour le client ID: ${id}`);

    // Vérifier d'abord si le client existe
    const clientCheck = await pool.query('SELECT nom, prenom FROM client WHERE idclient = $1', [id]);
    if (clientCheck.rows.length === 0) {
      console.log(`❌ Client ID ${id} n'existe pas dans la base`);
      return res.status(404).json({
        success: false,
        message: `Client ID ${id} non trouvé`,
        client_id: parseInt(id)
      });
    }

    const clientInfo = clientCheck.rows[0];
    console.log(`✅ Client trouvé: ${clientInfo.nom} ${clientInfo.prenom}`);

    // Requête pour récupérer les contrats
    const query = `
      SELECT
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        c.posx,
        c.posy,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM contract c
      INNER JOIN client cl ON c.idclient = cl.idclient
      WHERE c.idclient = $1
      ORDER BY c.datecontract DESC
    `;

    console.log('📡 Exécution de la requête SQL...');
    const result = await pool.query(query, [id]);

    console.log(`📊 RÉSULTAT: ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    if (result.rows.length > 0) {
      console.log('📋 CONTRATS TROUVÉS:');
      result.rows.forEach((contract, index) => {
        console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
        console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
        console.log(`      Marque compteur: ${contract.marquecompteur || 'Non définie'}`);
      });
    } else {
      console.log('⚠️ AUCUN CONTRAT TROUVÉ pour ce client');
    }

    const response = {
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`,
      client_id: parseInt(id),
      client_name: `${clientInfo.nom} ${clientInfo.prenom}`
    };

    console.log('📤 ENVOI DE LA RÉPONSE CONTRATS');
    res.json(response);

  } catch (error) {
    console.error('❌ ERREUR CRITIQUE lors de la récupération des contrats:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message,
      client_id: parseInt(req.params.id)
    });
  }
});

// Route pour récupérer tous les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/secteurs - Récupération de tous les secteurs');

    const query = 'SELECT ids, nom FROM secteur ORDER BY nom';
    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} secteur(s) récupéré(s)`);
    res.json({
      success: true,
      data: result.rows,
      message: `${result.rows.length} secteur(s) récupéré(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour récupérer un secteur spécifique par ID
app.get('/api/secteurs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/secteurs/${id}`);

    const query = 'SELECT ids, nom FROM secteur WHERE ids = $1';
    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Secteur non trouvé'
      });
    }

    console.log(`✅ Secteur ${id} récupéré: ${result.rows[0].nom}`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Secteur trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du secteur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du secteur',
      error: error.message
    });
  }
});

// Route pour récupérer tous les contrats
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        ct.idContract,
        ct.idClient,
        ct.codeQr,
        ct.dateContract,
        ct.marqueCompteur,
        ct.numSerieCompteur,
        ct.posX,
        ct.posY,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel,
        cl.email
      FROM Contract ct
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY cl.nom, cl.prenom
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer toutes les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        c.idCons,
        c.idCont,
        c.consommationPre,
        c.consommationActuelle,
        c.jours,
        c.periode,
        c.idTech,
        c.status,
        ct.codeQr,
        ct.marqueCompteur,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville
      FROM Consommation c
      LEFT JOIN Contract ct ON c.idCont = ct.idContract
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY c.idCons DESC
    `);

    res.json({
      success: true,
      data: result.rows
    });
  } catch (err) {
    console.error('Erreur lors de la récupération des consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 GET /api/contracts/${id}/last-consommation`);

    const query = `
      SELECT
        consommationactuelle,
        periode,
        jours
      FROM consommation
      WHERE idcont = $1
      ORDER BY periode DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length > 0) {
      console.log(`✅ Dernière consommation trouvée: ${result.rows[0].consommationactuelle} m³`);
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation trouvée'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: false,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route pour ajouter une nouvelle consommation avec génération automatique de facture
app.post('/api/consommations', async (req, res) => {
  try {
    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech, idtranch } = req.body;

    console.log('📝 Ajout d\'une nouvelle consommation:', req.body);

    // 1. Insérer la consommation dans la base de données
    const result = await pool.query(`
      INSERT INTO Consommation (idCont, consommationPre, consommationActuelle, jours, periode, idTech, idTranch, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [idcont, consommationpre || 0, consommationactuelle, jours || 30, periode, idtech || 1, idtranch || 1, 'nouveau']);

    const nouvelleConsommation = result.rows[0];
    console.log('✅ Consommation enregistrée:', nouvelleConsommation);

    // 2. Générer automatiquement la facture
    const factureResult = await generateFactureAutomatique(nouvelleConsommation);

    if (factureResult.success) {
      console.log('🧾 Facture générée automatiquement:', factureResult.data);

      // Retourner la consommation ET la facture générée
      res.json({
        success: true,
        data: nouvelleConsommation,
        factureGeneree: factureResult,
        message: 'Consommation enregistrée et facture générée automatiquement'
      });
    } else {
      console.log('⚠️ Erreur lors de la génération de facture:', factureResult.message);

      // Retourner la consommation même si la facture n'a pas pu être générée
      res.json({
        success: true,
        data: nouvelleConsommation,
        factureGeneree: factureResult,
        message: 'Consommation enregistrée mais erreur lors de la génération de facture'
      });
    }

  } catch (err) {
    console.error('❌ Erreur lors de l\'ajout de la consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message,
      message: 'Erreur lors de l\'enregistrement de la consommation'
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat spécifique
app.get('/api/contracts/:idcontract/last-consommation', async (req, res) => {
  try {
    const { idcontract } = req.params;

    const result = await pool.query(`
      SELECT
        c.idCons,
        c.consommationActuelle,
        c.consommationPre,
        c.periode,
        c.jours,
        c.status
      FROM Consommation c
      WHERE c.idCont = $1
      ORDER BY c.idCons DESC
      LIMIT 1
    `, [idcontract]);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        data: result.rows[0],
        message: `Dernière consommation trouvée pour le contrat ${idcontract}`
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée pour ce contrat'
      });
    }
  } catch (err) {
    console.error('Erreur lors de la récupération de la dernière consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la toute dernière consommation de la base de données (globale)
app.get('/api/last-consommation-global', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        c.idCons,
        c.consommationActuelle,
        c.consommationPre,
        c.periode,
        c.jours,
        c.status,
        ct.codeQr,
        cl.nom,
        cl.prenom
      FROM Consommation c
      LEFT JOIN Contract ct ON c.idCont = ct.idContract
      LEFT JOIN Client cl ON ct.idClient = cl.idClient
      ORDER BY c.idCons DESC
      LIMIT 1
    `);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        data: result.rows[0],
        message: 'Dernière consommation globale trouvée'
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'Aucune consommation trouvée dans la base de données'
      });
    }
  } catch (err) {
    console.error('Erreur lors de la récupération de la dernière consommation globale:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un client spécifique (tous ses contrats)
app.get('/api/clients/:idclient/last-consommation', async (req, res) => {
  try {
    const { idclient } = req.params;
    console.log(`📥 Requête GET /api/clients/${idclient}/last-consommation`);

    const query = `
      SELECT
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.periode,
        cons.jours,
        cons.status,
        cont.codeqr as contrat_reference,
        cont.idcontract
      FROM consommation cons
      INNER JOIN contract cont ON cons.idcont = cont.idcontract
      WHERE cont.idclient = $1
      ORDER BY cons.idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [idclient]);

    if (result.rows.length === 0) {
      console.log(`ℹ️ Aucune consommation trouvée pour le client ${idclient}`);
      return res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée pour ce client'
      });
    }

    const lastConsommation = result.rows[0];
    console.log(`✅ Dernière consommation trouvée pour le client ${idclient}:`, lastConsommation);

    res.json({
      success: true,
      data: {
        idcons: lastConsommation.idcons,
        consommationactuelle: lastConsommation.consommationactuelle,
        consommationpre: lastConsommation.consommationpre,
        periode: lastConsommation.periode,
        jours: lastConsommation.jours,
        status: lastConsommation.status,
        contrat_reference: lastConsommation.contrat_reference,
        idcontract: lastConsommation.idcontract
      },
      message: `Dernière consommation du client: ${lastConsommation.consommationactuelle} m³ (période: ${lastConsommation.periode})`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation du client',
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 3002;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Serveur Facturation démarré sur http://localhost:${PORT}`);
  console.log(`📱 Accessible depuis le réseau sur http://***********:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET  /api/clients (tous les clients)');
  console.log('  - GET  /api/clients/:id/contracts (contrats du client) ⭐ CRITIQUE');
  console.log('  - GET  /api/clients/:id/last-consommation (dernière consommation du client) ⭐ NOUVEAU');
  console.log('  - GET  /api/contracts/:id/last-consommation (dernière consommation)');
  console.log('  - GET  /api/contracts (tous les contrats)');
  console.log('  - GET  /api/consommations (toutes les consommations)');
  console.log('  - POST /api/consommations (ajouter consommation)');
  console.log('🧾 Routes de factures:');
  console.log('  - GET  /api/factures (toutes les factures)');
  console.log('  - GET  /api/factures/:id (détails facture)');
  console.log('  - GET  /api/factures/:id/pdf (télécharger PDF) 📄 NOUVEAU');
  console.log('  - PUT  /api/factures/:id/status (changer statut)');
  console.log('  - POST /api/factures (créer facture manuelle)');
  console.log('  - DELETE /api/factures/:id (supprimer facture)');
  console.log('✅ PRÊT À RECEVOIR LES REQUÊTES !');
});