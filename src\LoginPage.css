/* CSS exact selon l'image fournie */

.login-container {
  min-height: 100vh;
  background: #6366f1; /* Violet exact de l'image */
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 20px;
  box-sizing: border-box;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  box-sizing: border-box;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: -0.2px;
}

.login-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  font-weight: 400;
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #fecaca;
  font-size: 14px;
  text-align: center;
}

.login-form {
  width: 100%;
}

.input-group {
  margin-bottom: 24px;
}

.input-label {
  display: block;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  letter-spacing: 0px;
}

.login-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background-color: #f9fafb;
  box-sizing: border-box;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  color: #374151;
}

.login-input::placeholder {
  color: #9ca3af;
  font-size: 14px;
}

.login-input:focus {
  border-color: #6366f1;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.submit-button {
  width: 100%;
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  margin-top: 8px;
  min-height: 44px;
  touch-action: manipulation;
  letter-spacing: 0.5px;
}

.submit-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Media queries pour mobile */
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
    border-radius: 12px;
    max-width: 100%;
  }

  .card-header {
    margin-bottom: 28px;
  }

  .login-title {
    font-size: 22px;
  }

  .login-subtitle {
    font-size: 13px;
  }

  .input-group {
    margin-bottom: 20px;
  }

  .input-label {
    font-size: 13px;
  }

  .login-input {
    padding: 12px 14px;
    font-size: 16px; /* Garde 16px pour éviter le zoom sur iOS */
  }

  .submit-button {
    padding: 12px;
    font-size: 14px;
    margin-top: 6px;
    min-height: 48px;
  }
}

/* Media queries pour très petits écrans */
@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .login-card {
    padding: 24px 20px;
    border-radius: 12px;
  }

  .login-title {
    font-size: 20px;
  }

  .login-subtitle {
    font-size: 12px;
  }

  .input-group {
    margin-bottom: 18px;
  }

  .login-input {
    padding: 12px;
  }

  .submit-button {
    padding: 12px;
    min-height: 48px;
  }
}

/* Amélioration pour les appareils tactiles */
@media (hover: none) and (pointer: coarse) {
  .submit-button:hover {
    transform: none;
    box-shadow: none;
  }

  .submit-button:active {
    transform: scale(0.98);
    background: #1d4ed8;
  }
}
