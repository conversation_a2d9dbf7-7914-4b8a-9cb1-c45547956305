/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  background-color: #f5f5f5;
  color: #333;
  padding: 20px;
}

/* ===== LOGIN CONTAINER STYLES ===== */
.login-container {
  max-width: 400px;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 24px;
}

/* ===== FORM STYLES ===== */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-weight: 600;
  color: #34495e;
  font-size: 14px;
}

.form-input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* ===== BUTTON STYLES ===== */
.login-button {
  padding: 12px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background-color: #2980b9;
}

/* ===== MESSAGE STYLES ===== */
.login-message {
  text-align: center;
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.message-success {
  color: #27ae60;
  background-color: rgba(39, 174, 96, 0.1);
}

.message-error {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 480px) {
  .login-container {
    margin: 20px auto;
    padding: 20px;
  }
  
  .login-title {
    font-size: 20px;
  }
}