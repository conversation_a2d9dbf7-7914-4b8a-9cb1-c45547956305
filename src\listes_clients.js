import React, { useState, useEffect } from 'react';
import './TechnicianDashboard.css';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:4000';

const ListesClients = ({ onBack, onClientSelect }) => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [selectedClientLocation, setSelectedClientLocation] = useState(null);

  // Fonction pour récupérer les clients depuis la base de données "Facutration"
  const fetchClients = async () => {
    try {
      setLoading(true);
      console.log('🔄 Récupération des clients depuis la base "Facutration"...');

      const response = await fetch(`${API_BASE_URL}/api/clients`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Données reçues:', data);

      if (data.success && data.data) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients chargés depuis la base "Facutration"`);
      } else {
        setClients([]);
        console.warn('⚠️ Aucun client trouvé dans la réponse');
      }
      setError(null);
    } catch (err) {
      console.error('❌ Erreur lors de la récupération des clients:', err);

      let errorMessage = 'Impossible de charger les clients depuis la base "Facutration".';

      if (err.message.includes('Failed to fetch')) {
        errorMessage += ' Vérifiez que le serveur backend est démarré sur le port 4000.';
      } else if (err.message.includes('HTTP')) {
        errorMessage += ` Erreur serveur: ${err.message}`;
      } else {
        errorMessage += ' Vérifiez votre connexion.';
      }

      setError(errorMessage);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  // Filtrer les clients selon le terme de recherche (structure base "Facutration")
  const filteredClients = clients.filter(client =>
    client.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.adresse?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.ville?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.tel?.includes(searchTerm) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.idclient?.toString().includes(searchTerm)
  );

  const handleRefresh = () => {
    fetchClients();
  };

  // Fonction pour récupérer les informations de localisation (secteur + contrat)
  const handleLocalisation = async (client) => {
    try {
      console.log(`🗺️ Récupération des informations de localisation pour le client ${client.idclient}...`);

      // 1. Récupérer le secteur du client
      const secteurResponse = await fetch(`${API_BASE_URL}/api/secteurs/${client.ids}`);
      const secteurData = await secteurResponse.json();

      // 2. Récupérer le contrat du client (avec coordonnées posX, posY)
      const contractResponse = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/contracts`);
      const contractData = await contractResponse.json();

      let locationInfo = {
        client: client,
        secteur: null,
        contrat: null,
        coordinates: null
      };

      // Traitement du secteur
      if (secteurData.success && secteurData.data) {
        locationInfo.secteur = secteurData.data;
        console.log(`✅ Secteur trouvé: ${secteurData.data.nom}`);
      } else {
        console.log('⚠️ Secteur non trouvé pour ce client');
      }

      // Traitement du contrat
      if (contractData.success && contractData.data && contractData.data.length > 0) {
        const contrat = contractData.data[0]; // Prendre le premier contrat
        locationInfo.contrat = contrat;

        // Vérifier si les coordonnées existent
        if (contrat.posx && contrat.posy) {
          locationInfo.coordinates = {
            x: contrat.posx,
            y: contrat.posy
          };
          console.log(`✅ Coordonnées trouvées: (${contrat.posx}, ${contrat.posy})`);
        } else {
          console.log('⚠️ Coordonnées non définies dans le contrat');
        }
      } else {
        console.log('⚠️ Aucun contrat trouvé pour ce client');
      }

      // Afficher les informations de localisation
      setSelectedClientLocation(locationInfo);
      setShowLocationModal(true);

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des informations de localisation:', error);
      alert('❌ Erreur lors de la récupération des informations de localisation');
    }
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button
              onClick={onBack}
              className="tech-mobile-back-btn"
            >
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des clients...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button
              onClick={onBack}
              className="tech-mobile-back-btn"
            >
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-error">
            <div className="tech-mobile-error-icon">⚠️</div>
            <h3>Erreur de chargement</h3>
            <p>{error}</p>
            <button
              onClick={handleRefresh}
              className="tech-mobile-action-btn complete"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button
            onClick={onBack}
            className="tech-mobile-back-btn"
          >
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
            <p className="tech-mobile-card-subtitle">
              {filteredClients.length} client(s) trouvé(s)
            </p>
          </div>
          <button
            onClick={handleRefresh}
            className="tech-mobile-refresh-btn"
          >
            🔄
          </button>
        </div>
      </div>

      {/* Barre de recherche */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-search-container">
          <input
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="tech-mobile-search-input"
          />
          <div className="tech-mobile-search-icon">🔍</div>
        </div>
      </div>

      {/* Liste des clients */}
      {filteredClients.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">👥</div>
            <h3>Aucun client trouvé</h3>
            <p>
              {searchTerm
                ? 'Aucun client ne correspond à votre recherche.'
                : 'Aucun client enregistré dans la base de données.'
              }
            </p>
          </div>
        </div>
      ) : (
        filteredClients.map(client => (
          <div key={client.idclient} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{client.nom} {client.prenom}</strong>
              </div>
              <div className="tech-mobile-intervention-badge">
                ID: {client.idclient}
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              <div className="tech-mobile-intervention-info">
                <span>📍 {client.adresse}</span>
              </div>
              {client.ville && (
                <div className="tech-mobile-intervention-info">
                  <span>🏙️ {client.ville}</span>
                </div>
              )}
              {client.tel && (
                <div className="tech-mobile-intervention-info">
                  <span>📞 {client.tel}</span>
                </div>
              )}
              {client.email && (
                <div className="tech-mobile-intervention-info">
                  <span>📧 {client.email}</span>
                </div>
              )}
              {client.ids && (
                <div className="tech-mobile-intervention-info">
                  <span>🏢 Secteur: {client.ids}</span>
                </div>
              )}
            </div>

            <div className="tech-mobile-intervention-actions">
              <button
                className="tech-mobile-action-btn start"
                onClick={() => handleLocalisation(client)}
              >
                Localiser
              </button>
              <button
                className="tech-mobile-action-btn complete"
                onClick={() => {
                  if (onClientSelect) {
                    onClientSelect(client);
                  }
                }}
              >
                Sélectionner
              </button>
            </div>
          </div>
        ))
      )}

      {/* Modal d'informations de localisation */}
      {showLocationModal && selectedClientLocation && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '12px',
            maxWidth: '90%',
            maxHeight: '80%',
            overflow: 'auto',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{ marginBottom: '20px', textAlign: 'center' }}>
              <h2 style={{ color: '#2196F3', marginBottom: '10px' }}>
                🗺️ Informations de Localisation
              </h2>
              <h3 style={{ color: '#333', marginBottom: '20px' }}>
                {selectedClientLocation.client.nom} {selectedClientLocation.client.prenom}
              </h3>
            </div>

            {/* Informations du secteur */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
              <h4 style={{ color: '#4caf50', marginBottom: '10px' }}>📍 Secteur</h4>
              {selectedClientLocation.secteur ? (
                <div>
                  <p><strong>Nom du secteur:</strong> {selectedClientLocation.secteur.nom}</p>
                  <p><strong>ID Secteur:</strong> {selectedClientLocation.secteur.ids}</p>
                </div>
              ) : (
                <p style={{ color: '#ff9800' }}>⚠️ Secteur non défini pour ce client</p>
              )}
            </div>

            {/* Informations du contrat */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
              <h4 style={{ color: '#9c27b0', marginBottom: '10px' }}>📋 Contrat</h4>
              {selectedClientLocation.contrat ? (
                <div>
                  <p><strong>Code QR:</strong> {selectedClientLocation.contrat.codeqr || 'Non défini'}</p>
                  <p><strong>Marque compteur:</strong> {selectedClientLocation.contrat.marquecompteur || 'Non définie'}</p>
                  <p><strong>N° série:</strong> {selectedClientLocation.contrat.numseriecompteur || 'Non défini'}</p>
                  <p><strong>Date contrat:</strong> {selectedClientLocation.contrat.datecontract ? new Date(selectedClientLocation.contrat.datecontract).toLocaleDateString('fr-FR') : 'Non définie'}</p>
                </div>
              ) : (
                <p style={{ color: '#ff9800' }}>⚠️ Aucun contrat trouvé pour ce client</p>
              )}
            </div>

            {/* Coordonnées de localisation */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
              <h4 style={{ color: '#ff5722', marginBottom: '10px' }}>🎯 Coordonnées</h4>
              {selectedClientLocation.coordinates ? (
                <div>
                  <p><strong>Position X:</strong> {selectedClientLocation.coordinates.x}</p>
                  <p><strong>Position Y:</strong> {selectedClientLocation.coordinates.y}</p>
                  <button
                    onClick={() => {
                      // Ouvrir Google Maps avec les coordonnées
                      const url = `https://www.google.com/maps/search/${selectedClientLocation.coordinates.y},${selectedClientLocation.coordinates.x}`;
                      window.open(url, '_blank');
                    }}
                    style={{
                      marginTop: '10px',
                      padding: '8px 16px',
                      backgroundColor: '#4caf50',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    🗺️ Ouvrir dans Google Maps
                  </button>
                </div>
              ) : (
                <div>
                  <p style={{ color: '#ff9800' }}>⚠️ Coordonnées non définies dans le contrat</p>
                  <button
                    onClick={() => {
                      // Fallback: utiliser l'adresse du client
                      const searchQuery = encodeURIComponent(`${selectedClientLocation.client.adresse}, ${selectedClientLocation.client.ville || ''}`);
                      const url = `https://www.google.com/maps/search/${searchQuery}`;
                      window.open(url, '_blank');
                    }}
                    style={{
                      marginTop: '10px',
                      padding: '8px 16px',
                      backgroundColor: '#2196F3',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    🗺️ Rechercher par adresse
                  </button>
                </div>
              )}
            </div>

            {/* Boutons d'action */}
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
              <button
                onClick={() => {
                  setShowLocationModal(false);
                  setSelectedClientLocation(null);
                }}
                style={{
                  padding: '10px 20px',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ListesClients;