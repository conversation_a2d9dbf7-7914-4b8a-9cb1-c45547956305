import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const API_BASE_URL = 'http://localhost:4001';

const HistoriquePage = ({ onBack }) => {
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sectorFilter, setSectorFilter] = useState('all');
  const [secteurs, setSecteurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // États pour le modal de localisation
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [selectedClientLocation, setSelectedClientLocation] = useState(null);
  const [locationLoading, setLocationLoading] = useState(false);

  useEffect(() => {
    loadClients();
    loadSecteurs();
  }, []);

  useEffect(() => {
    filterClients();
  }, [clients, searchTerm, sectorFilter]);

  const loadClients = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('📥 Chargement des clients depuis la base de données...');

      const response = await fetch(`${API_BASE_URL}/api/clients`);
      const data = await response.json();

      if (data.success) {
        console.log(`✅ ${data.data.length} clients récupérés`);
        setClients(data.data);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des clients');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des clients:', error);
      setError('Erreur lors du chargement des clients: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadSecteurs = async () => {
    try {
      console.log('📥 Chargement des secteurs...');
      const response = await fetch(`${API_BASE_URL}/api/secteurs`);
      const data = await response.json();

      if (data.success) {
        console.log(`✅ ${data.data.length} secteurs récupérés`);
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des secteurs:', error);
    }
  };

  const filterClients = () => {
    let filtered = clients;

    // Filtrage par terme de recherche
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(client =>
        client.nom.toLowerCase().includes(term) ||
        client.prenom.toLowerCase().includes(term) ||
        client.ville.toLowerCase().includes(term) ||
        client.email.toLowerCase().includes(term) ||
        client.tel.includes(term)
      );
    }

    // Filtrage par secteur
    if (sectorFilter !== 'all') {
      filtered = filtered.filter(client => client.secteur_id === parseInt(sectorFilter));
    }

    setFilteredClients(filtered);
  };

  const handleLocalisation = async (client) => {
    try {
      setLocationLoading(true);
      console.log(`🗺️ Localisation demandée pour le client: ${client.nom} ${client.prenom}`);
      console.log(`🔍 Client data:`, client);

      // Étape 1: Récupérer les informations du secteur
      console.log(`📡 Appel API secteur: ${API_BASE_URL}/api/secteurs/${client.ids}`);
      const secteurResponse = await fetch(`${API_BASE_URL}/api/secteurs/${client.ids}`);
      console.log(`📡 Secteur response status:`, secteurResponse.status);

      if (!secteurResponse.ok) {
        throw new Error(`Erreur secteur: ${secteurResponse.status} ${secteurResponse.statusText}`);
      }

      const secteurData = await secteurResponse.json();
      console.log(`📡 Secteur data:`, secteurData);

      let secteurNom = 'Secteur non défini';
      if (secteurData.success && secteurData.data) {
        secteurNom = secteurData.data.nom;
      }

      // Étape 2: Récupérer le contrat du client avec les coordonnées posX et posY
      console.log(`📡 Appel API contrat: ${API_BASE_URL}/api/clients/${client.idclient}/contracts`);
      const contractResponse = await fetch(`${API_BASE_URL}/api/clients/${client.idclient}/contracts`);
      console.log(`📡 Contrat response status:`, contractResponse.status);

      if (!contractResponse.ok) {
        throw new Error(`Erreur contrat: ${contractResponse.status} ${contractResponse.statusText}`);
      }

      const contractData = await contractResponse.json();
      console.log(`📡 Contrat data:`, contractData);

      console.log(`📍 Secteur: ${secteurNom}`);
      console.log(`📋 Contrat:`, contractData);

      // Préparer les données pour le modal
      const locationInfo = {
        client: client,
        secteur: secteurNom,
        contrat: null,
        coordinates: null
      };

      // Vérifier si le client a un contrat avec des coordonnées
      if (contractData.success && contractData.data && contractData.data.length > 0) {
        const contract = contractData.data[0]; // Prendre le premier contrat
        locationInfo.contrat = contract;

        if (contract.posx && contract.posy) {
          const latitude = parseFloat(contract.posy);
          const longitude = parseFloat(contract.posx);

          if (!isNaN(latitude) && !isNaN(longitude)) {
            locationInfo.coordinates = {
              x: contract.posx,
              y: contract.posy,
              latitude: latitude,
              longitude: longitude
            };
          }
        }
      }

      // Afficher le modal avec les informations
      setSelectedClientLocation(locationInfo);
      setShowLocationModal(true);

    } catch (error) {
      console.error('❌ Erreur lors de la localisation:', error);
      alert('❌ Erreur lors de la récupération des informations de localisation');
    } finally {
      setLocationLoading(false);
    }
  };

  const getSecteurName = (secteurId) => {
    const secteur = secteurs.find(s => s.ids === secteurId);
    return secteur ? secteur.nom : 'Non défini';
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des clients...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-error">
            <div className="tech-mobile-error-icon">❌</div>
            <h3>Erreur de chargement</h3>
            <p>{error}</p>
            <button
              onClick={loadClients}
              className="tech-mobile-action-btn start"
            >
              🔄 Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
            <p className="tech-mobile-card-subtitle">
              {filteredClients.length} client(s) • Base de données Facutration
            </p>
          </div>
        </div>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">🔍 Recherche et Filtres</h2>
          <button
            onClick={loadClients}
            className="tech-mobile-action-btn start"
            style={{ fontSize: '12px', padding: '6px 12px' }}
          >
            � Actualiser
          </button>
        </div>

        {/* Barre de recherche */}
        <div style={{ marginBottom: '15px' }}>
          <input
            type="text"
            placeholder="🔍 Rechercher par nom, prénom, ville, email ou téléphone..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              fontSize: '16px',
              outline: 'none',
              transition: 'border-color 0.2s'
            }}
            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
            onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
          />
        </div>

        {/* Filtre par secteur */}
        <div className="tech-mobile-filter-container">
          <select
            value={sectorFilter}
            onChange={(e) => setSectorFilter(e.target.value)}
            style={{
              width: '100%',
              padding: '10px',
              border: '2px solid #e5e7eb',
              borderRadius: '6px',
              fontSize: '14px',
              backgroundColor: 'white'
            }}
          >
            <option value="all">🏘️ Tous les secteurs ({clients.length})</option>
            {secteurs.map(secteur => (
              <option key={secteur.ids} value={secteur.ids}>
                📍 {secteur.nom} ({clients.filter(c => c.ids === secteur.ids).length})
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Liste des clients */}
      {filteredClients.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">�</div>
            <h3>Aucun client trouvé</h3>
            <p>
              {searchTerm || sectorFilter !== 'all'
                ? 'Aucun client ne correspond aux critères de recherche.'
                : 'Aucun client n\'est enregistré dans la base de données.'
              }
            </p>
            {(searchTerm || sectorFilter !== 'all') && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSectorFilter('all');
                }}
                className="tech-mobile-action-btn start"
                style={{ marginTop: '10px' }}
              >
                🔄 Réinitialiser les filtres
              </button>
            )}
          </div>
        </div>
      ) : (
        filteredClients.map(client => (
          <div key={client.idclient} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '20px' }}>👤</span>
                  <strong>{client.nom} {client.prenom}</strong>
                </div>
                <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '4px' }}>
                  📍 {client.adresse}
                  {client.ville && `, ${client.ville}`}
                </div>
                <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '2px' }}>
                  🏘️ Secteur: {getSecteurName(client.ids)}
                </div>
              </div>
              <div className="tech-mobile-intervention-actions">
                <button
                  onClick={() => handleLocalisation(client)}
                  className="tech-mobile-action-btn start"
                  style={{
                    fontSize: '12px',
                    padding: '6px 10px',
                    backgroundColor: '#10b981',
                    marginBottom: '5px'
                  }}
                >
                  🗺️ Localiser
                </button>
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              {client.tel && (
                <div className="tech-mobile-intervention-info">
                  <span>� {client.tel}</span>
                </div>
              )}
              {client.email && (
                <div className="tech-mobile-intervention-info">
                  <span>� {client.email}</span>
                </div>
              )}
            </div>
          </div>
        ))
      )}

      {/* Statistiques des clients */}
      {clients.length > 0 && (
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <h2 className="tech-mobile-card-title">📊 Statistiques des Clients</h2>
          </div>
          <div className="tech-mobile-stats-grid">
            <div className="tech-mobile-stat-card">
              <div className="tech-mobile-stat-icon">👥</div>
              <div className="tech-mobile-stat-number">
                {clients.length}
              </div>
              <div className="tech-mobile-stat-label">Total Clients</div>
            </div>
            <div className="tech-mobile-stat-card">
              <div className="tech-mobile-stat-icon">🔍</div>
              <div className="tech-mobile-stat-number">
                {filteredClients.length}
              </div>
              <div className="tech-mobile-stat-label">Affichés</div>
            </div>
            <div className="tech-mobile-stat-card">
              <div className="tech-mobile-stat-icon">🏘️</div>
              <div className="tech-mobile-stat-number">
                {secteurs.length}
              </div>
              <div className="tech-mobile-stat-label">Secteurs</div>
            </div>
            <div className="tech-mobile-stat-card">
              <div className="tech-mobile-stat-icon">�</div>
              <div className="tech-mobile-stat-number">
                {clients.filter(c => c.email && c.email.trim()).length}
              </div>
              <div className="tech-mobile-stat-label">Avec Email</div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de localisation */}
      {showLocationModal && selectedClientLocation && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '24px',
            maxWidth: '500px',
            width: '90%',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
          }}>
            {/* En-tête du modal */}
            <div style={{ marginBottom: '20px', textAlign: 'center' }}>
              <h2 style={{ color: '#1e40af', marginBottom: '8px' }}>
                🗺️ Localisation Client
              </h2>
              <p style={{ color: '#6b7280', fontSize: '14px' }}>
                Informations de localisation basées sur le contrat
              </p>
            </div>

            {/* Informations du client */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8fafc', borderRadius: '8px' }}>
              <h3 style={{ color: '#1f2937', marginBottom: '10px' }}>👤 Client</h3>
              <p><strong>Nom:</strong> {selectedClientLocation.client.nom} {selectedClientLocation.client.prenom}</p>
              <p><strong>Adresse:</strong> {selectedClientLocation.client.adresse}</p>
              {selectedClientLocation.client.ville && (
                <p><strong>Ville:</strong> {selectedClientLocation.client.ville}</p>
              )}
            </div>

            {/* Informations du secteur */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f0f9ff', borderRadius: '8px' }}>
              <h3 style={{ color: '#1e40af', marginBottom: '10px' }}>🏘️ Secteur</h3>
              <p><strong>Secteur:</strong> {selectedClientLocation.secteur}</p>
            </div>

            {/* Informations du contrat */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f0fdf4', borderRadius: '8px' }}>
              <h3 style={{ color: '#16a34a', marginBottom: '10px' }}>📋 Contrat</h3>
              {selectedClientLocation.contrat ? (
                <div>
                  <p><strong>Code QR:</strong> {selectedClientLocation.contrat.codeqr || 'Non défini'}</p>
                  <p><strong>Marque Compteur:</strong> {selectedClientLocation.contrat.marquecompteur || 'Non définie'}</p>
                  <p><strong>Numéro Série:</strong> {selectedClientLocation.contrat.numseriecompteur || 'Non défini'}</p>
                  <p><strong>Date Contrat:</strong> {selectedClientLocation.contrat.datecontract ? new Date(selectedClientLocation.contrat.datecontract).toLocaleDateString() : 'Non définie'}</p>
                </div>
              ) : (
                <p style={{ color: '#ff9800' }}>⚠️ Aucun contrat trouvé pour ce client</p>
              )}
            </div>

            {/* Coordonnées de localisation */}
            <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#fef3c7', borderRadius: '8px' }}>
              <h3 style={{ color: '#d97706', marginBottom: '10px' }}>🎯 Coordonnées (Table Contract)</h3>
              {selectedClientLocation.coordinates ? (
                <div>
                  <p><strong>Position X:</strong> {selectedClientLocation.coordinates.x}</p>
                  <p><strong>Position Y:</strong> {selectedClientLocation.coordinates.y}</p>
                  <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px' }}>
                    Coordonnées précises trouvées dans le contrat
                  </p>
                </div>
              ) : (
                <div>
                  <p style={{ color: '#dc2626' }}>❌ Pas de coordonnées dans le contrat</p>
                  <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px' }}>
                    La recherche se fera par adresse
                  </p>
                </div>
              )}
            </div>

            {/* Boutons d'action */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
              <button
                onClick={() => {
                  if (selectedClientLocation.coordinates) {
                    // Utiliser les coordonnées précises du contrat
                    const { latitude, longitude } = selectedClientLocation.coordinates;
                    const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}&z=18`;
                    window.open(googleMapsUrl, '_blank');
                    console.log(`✅ Google Maps ouvert avec coordonnées du contrat: ${latitude}, ${longitude}`);
                  } else {
                    // Utiliser l'adresse du client
                    const searchQuery = encodeURIComponent(`${selectedClientLocation.client.adresse}, ${selectedClientLocation.client.ville || ''}`);
                    const googleMapsUrl = `https://www.google.com/maps/search/${searchQuery}`;
                    window.open(googleMapsUrl, '_blank');
                    console.log(`✅ Google Maps ouvert avec recherche par adresse: ${selectedClientLocation.client.adresse}`);
                  }
                  setShowLocationModal(false);
                  setSelectedClientLocation(null);
                }}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: '600'
                }}
              >
                🗺️ Ouvrir Google Maps
              </button>

              <button
                onClick={() => {
                  setShowLocationModal(false);
                  setSelectedClientLocation(null);
                }}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: '600'
                }}
              >
                ❌ Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoriquePage;
