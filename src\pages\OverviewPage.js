import React from 'react';
import '../TechnicianDashboard.css';

const OverviewPage = ({ user, onNavigate, technicianInfo }) => {
  const quickActions = [
    {
      id: 'scanner',
      title: 'Scanner QR',
      description: 'Identifier un compteur',
      icon: '📱',
      color: '#6366f1'
    },
    {
      id: 'consommation',
      title: 'Nouveau Relevé',
      description: 'Saisir consommation',
      icon: '💧',
      color: '#059669'
    },
    {
      id: 'clients',
      title: 'CLIENTS →',
      description: 'Liste des clients',
      icon: '👥',
      color: '#6c5ce7'
    },
    {
      id: 'saisie-client',
      title: 'Nouveau Client',
      description: 'Ajouter un client',
      icon: '👤',
      color: '#f59e0b'
    },
    {
      id: 'map',
      title: 'Localisation',
      description: 'Carte des clients',
      icon: '🗺️',
      color: '#7c3aed'
    }
  ];

  return (
    <div className="tech-mobile-content">
      {/* En-tête de bienvenue */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <div>
            <h1 className="tech-mobile-card-title">
              {technicianInfo ?
                ` ${technicianInfo.prenom} ${technicianInfo.nom} ` :
                'Bonjour Technicien '
              }
            </h1>
            <p className="tech-mobile-card-subtitle">
              {new Date().toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
        </div>
      </div>



      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          {/* <h2 className="tech-mobile-card-title">⚡ Actions Rapides</h2> */}
        </div>
        <div className="tech-mobile-quick-actions">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="tech-mobile-quick-action"
              onClick={() => onNavigate(action.id)}
              style={{ borderColor: action.color }}
            >
              <div
                className="tech-mobile-quick-action-icon"
                style={{ backgroundColor: action.color }}
              >
                {action.icon}
              </div>
              <div className="tech-mobile-quick-action-content">
                <div className="tech-mobile-quick-action-title">{action.title}</div>
                <div className="tech-mobile-quick-action-desc">{action.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OverviewPage;
