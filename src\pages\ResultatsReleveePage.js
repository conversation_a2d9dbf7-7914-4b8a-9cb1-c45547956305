import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const ResultatsReleveePage = ({ onBack, newReleve }) => {
  const [releves, setReleves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const API_BASE_URL = 'http://localhost:3003';

  // Afficher seulement le nouveau relevé qui vient d'être enregistré
  useEffect(() => {
    if (newReleve) {
      console.log('📊 Affichage du nouveau relevé enregistré:', newReleve);

      // Formater le nouveau relevé pour l'affichage
      const formattedReleve = {
        id: newReleve.id || Date.now(),
        client: newReleve.client || 'Client',
        periode: newReleve.periode || '2025-07',
        compteur: newReleve.compteur || 'QR123',
        precedente: `${newReleve.consommationpre || 0} m³`,
        actuelle: `${newReleve.consommationactuelle || 0} m³`,
        consommation: `${(parseFloat(newReleve.consommationactuelle || 0) - parseFloat(newReleve.consommationpre || 0))} m³`,
        technicien: newReleve.technicien || 'Technicien',
        adresse: newReleve.adresse || 'Adresse non disponible',
        jours: newReleve.jours || 30,
        status: 'nouveau',
        dateEnregistrement: new Date().toISOString()
      };

      setReleves([formattedReleve]); // Afficher seulement ce relevé
      setLoading(false);
      console.log('✅ Nouveau relevé formaté pour affichage:', formattedReleve);
    } else {
      console.warn('⚠️ Aucun nouveau relevé à afficher');
      setReleves([]);
      setLoading(false);
    }
  }, [newReleve]);

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Résultats des Relevés</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des résultats...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Erreur</h1>
          </div>
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <p style={{ color: '#ef4444', marginBottom: '20px' }}>{error}</p>
            <button
              onClick={onBack}
              className="tech-mobile-action-btn"
              style={{ margin: '10px auto' }}
            >
              Retour au formulaire
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      {/* En-tête */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Relevé Enregistré</h1>
            <p className="tech-mobile-card-subtitle">Le relevé a été enregistré avec succès dans la base Facutration</p>
          </div>
        </div>
      </div>

      {/* Message de succès si nouveau relevé */}
      {newReleve && (
        <div className="tech-mobile-card" style={{ 
          background: 'linear-gradient(45deg, #10b981, #059669)', 
          color: 'white',
          marginBottom: '20px'
        }}>
          <div style={{ textAlign: 'center', padding: '10px' }}>
            <h3 style={{ margin: '0 0 5px 0', fontSize: '18px' }}>✅ Relevé Enregistré</h3>
            <p style={{ margin: '0', fontSize: '14px', opacity: '0.9' }}>
              Le relevé a été enregistré avec succès dans la base de données
            </p>
          </div>
        </div>
      )}

      {/* Affichage du nouveau relevé enregistré */}
      {releves.length > 0 ? (
        <div className="releves-list">
          {releves.map((releve, index) => (
            <div key={releve.id} className="releve-result-card" style={{ marginBottom: '15px' }}>
              <div className="releve-result-header">
                <div className="releve-client-name">
                  <strong>{releve.client}</strong>
                  <small style={{ color: '#6b7280', fontSize: '12px', display: 'block' }}>
                    Relevé #{releve.id} - {releve.status}
                  </small>
                </div>
                <div className="releve-consommation-badge">
                  {releve.consommation}
                </div>
              </div>

              <div className="releve-result-content">
                <div className="releve-info-row">
                  <div className="releve-info-item">
                    <span className="releve-icon">📅</span>
                    <span className="releve-label">Période:</span>
                    <span className="releve-value">{releve.periode}</span>
                  </div>
                  <div className="releve-info-item">
                    <span className="releve-icon">🔢</span>
                    <span className="releve-label">Compteur:</span>
                    <span className="releve-value">{releve.compteur}</span>
                  </div>
                </div>

                <div className="releve-info-row">
                  <div className="releve-info-item">
                    <span className="releve-icon">👤</span>
                    <span className="releve-label">Technicien:</span>
                    <span className="releve-value">{releve.technicien}</span>
                  </div>
                  <div className="releve-info-item">
                    <span className="releve-icon">📊</span>
                    <span className="releve-label">Précédente:</span>
                    <span className="releve-value">{releve.precedente}</span>
                  </div>
                </div>

                <div className="releve-info-row">
                  <div className="releve-info-item">
                    <span className="releve-icon">💧</span>
                    <span className="releve-label">Actuelle:</span>
                    <span className="releve-value">{releve.actuelle}</span>
                  </div>
                  <div className="releve-info-item">
                    <span className="releve-icon">⏱️</span>
                    <span className="releve-label">Jours:</span>
                    <span className="releve-value">{releve.jours}</span>
                  </div>
                </div>

                <div className="releve-address">
                  <span className="releve-icon">📍</span>
                  <span className="releve-address-text">{releve.adresse}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="tech-mobile-card" style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>📊</div>
          <h3 style={{ color: '#6b7280', marginBottom: '10px' }}>Aucun relevé à afficher</h3>
          <p style={{ color: '#9ca3af' }}>Le nouveau relevé enregistré apparaîtra ici</p>
          <button
            onClick={onBack}
            className="tech-mobile-action-btn"
            style={{ marginTop: '20px' }}
          >
            Retour au formulaire
          </button>
        </div>
      )}

      {/* Navigation en bas comme dans l'image */}
      <div className="releve-bottom-navigation">
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📊</div>
          <div className="releve-nav-label">VUE D'ENSEMBLE</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">👥</div>
          <div className="releve-nav-label">CLIENTS</div>
        </div>
        <div className="releve-nav-item active">
          <div className="releve-nav-icon">💧</div>
          <div className="releve-nav-label">CONSOMMATION</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📄</div>
          <div className="releve-nav-label">FACTURES</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">📱</div>
          <div className="releve-nav-label">SCANNER QR</div>
        </div>
        <div className="releve-nav-item">
          <div className="releve-nav-icon">🗺️</div>
          <div className="releve-nav-label">LOCALISATION</div>
        </div>
      </div>
    </div>
  );
};

export default ResultatsReleveePage;
